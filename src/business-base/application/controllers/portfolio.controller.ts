import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  StreamableFile,
  UploadedFile,
  UseInterceptors,
  Version,
} from '@nestjs/common';
import { PortfolioDto } from '@business-base/application/dto/in/portfolio.dto';
import { UpdatePortfolioDto } from '@business-base/application/dto/in/update-portfolio.dto';
import { PortfolioUseCase } from '@business-base/application/use-cases/portfolio.use-case';
import { FileInterceptor } from '@nestjs/platform-express';
import { Express, Request } from 'express';
import { AccountRole, CommunicationChannel, UserRoleInAccount } from '@common/enums';
import { UserRolesInAccount } from '@common/auth/decorators/user-role-in-account.decorator';
import { AccountRoles } from '@common/auth/decorators/account-role.decorator';
import { SelectedColumnsDto } from '@business-base/application/dto/in/selected-columns-dto';
import { PortfolioSearchQueryDto } from '@business-base/application/dto/in/portfolio-search-query.dto';
import {
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ResponsePortfolioDto } from '@business-base/application/dto/out/response-portfolio.dto';

@ApiTags('Portfolios')
@UserRolesInAccount(UserRoleInAccount.ADMIN)
@AccountRoles(AccountRole.BASIC)
@Controller('business-base/portfolios')
export class PortfolioController {
  constructor(private readonly portfolioUseCase: PortfolioUseCase) {}

  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Import a new portfolio',
    description: `
    This endpoint is used to import a new portfolio from a csv file. The csv file should have the valid headers according to the workflow.

    **Customer Preferences Integration:**
    - If customer preferences are configured, the portfolio will automatically use the customer's default workflow ID if not explicitly provided
    - Follow-up settings (workflow, cron expression, quantity, interval) will be applied from customer preferences
    - Timezone settings from customer preferences will be considered for scheduling
    - If customer preferences are not available or cannot be retrieved, the system will use the explicitly provided values or defaults
    `,
  })
  @ApiBody({
    description: 'The portfolio import data and csv file',
    type: 'multipart/form-data',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'The CSV file containing portfolio data',
        },
        name: {
          type: 'string',
          description: 'Name of the portfolio',
          example: 'Portfolio 1',
        },
        workflowId: {
          type: 'string',
          description:
            'ID of the workflow to associate with the portfolio. If not provided and customer preferences exist, the default workflow from preferences will be used.',
          example: '43e3ceed-58da-4ff2-bddf-67cb79d4433f',
        },
        workExpression: {
          type: 'string',
          description: 'Work expression for the portfolio',
          example: 'Work Expression',
        },
        idleAfter: {
          type: 'string',
          description: 'Number of minutes after which the portfolio should become idle',
          example: '10',
        },
        executeImmediately: {
          type: 'string',
          description: 'Whether to execute the portfolio immediately (true/false)',
          example: 'true',
        },
        communicationChannel: {
          type: 'string',
          description: 'Communication channel for the portfolio',
          enum: Object.values(CommunicationChannel),
          example: CommunicationChannel.WHATSAPPSELFHOSTED,
        },
        timezoneUTC: {
          type: 'string',
          description: 'Timezone in UTC for scheduling the portfolio',
          example: '-3',
        },
        followUpCronExpression: {
          type: 'string',
          description: 'Cron expression for follow-up tasks',
          example: '0 11-23 * * 1-5',
        },
        followUpQuantity: {
          type: 'string',
          description: 'Number of follow-up tasks to create',
          example: '1',
        },
        followUpAfter: {
          type: 'string',
          description: 'Number of minutes after which the follow-up tasks should be created',
          example: '120',
        },
        followUpWorkflowId: {
          type: 'string',
          description: 'ID of the workflow to associate with follow-up tasks',
          example: '6f413811-4aa8-43f4-8c48-d00143dd226d',
        },
      },
      required: [
        'file',
        'name',
        'workflowId',
        'workExpression',
        'idleAfter',
        'executeImmediately',
        'communicationChannel',
      ],
    },
  })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({
    status: 201,
    description: 'The portfolio was created successfully',
    type: ResponsePortfolioDto,
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number' },
        data: { type: 'object', properties: { id: { type: 'string' } } },
      },
    },
    examples: {
      example: {
        summary: 'Example response',
        value: {
          statusCode: 201,
          data: {
            id: '123',
            name: 'Portfolio 1',
            workflowId: '123',
            workExpression: 'Work Expression',
            idleAfter: 10,
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid request body',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number' },
        message: { type: 'string' },
      },
    },
    examples: {
      example: {
        summary: 'Example response',
        value: {
          statusCode: 400,
          message: 'Invalid request body',
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid credentials',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number' },
        timestamp: { type: 'string' },
        message: { type: 'array', items: { type: 'string' } },
        data: {
          type: 'object',
          properties: { path: { type: 'string' }, errorStatus: { type: 'number' } },
        },
      },
    },
    examples: {
      example: {
        summary: 'Example response',
        value: {
          statusCode: 401,
          timestamp: '2025-06-06T13:22:16.588Z',
          message: ['You must be authenticated to access this route'],
          data: {
            path: '/api/v1/business-base/portfolios/import',
            errorStatus: 9,
          },
        },
      },
    },
  })
  @Post('/import')
  @Version('1')
  @UseInterceptors(FileInterceptor('file'))
  async create(
    @Body('name') name: string,
    @Body('workflowId') workflowId: string,
    @Body('workExpression') workExpression: string,
    @Body('executeImmediately') executeImmediately: string,
    @Body('communicationChannel') communicationChannel: string,
    @UploadedFile() file: Express.Multer.File,
    @Req() request: Request,
    @Body('idleAfter') idleAfter?: string,
    @Body('timezoneUTC') timezoneUTC?: string,
    @Body('followUpCronExpression') followUpCronExpression?: string,
    @Body('followUpQuantity') followUpQuantity?: string,
    @Body('followUpAfter') followUpAfter?: string,
    @Body('followUpWorkflowId') followUpWorkflowId?: string,
  ): Promise<any> {
    const executeImmediatelyBoolean = executeImmediately?.toLowerCase() === 'true';
    const idleAfterNumber = parseInt(idleAfter, 10) || null;
    const communicationChannelEnum = CommunicationChannel[communicationChannel];

    const createPortfolioDto: PortfolioDto = {
      name,
      workflowId,
      workExpression,
      communicationChannel: communicationChannelEnum,
      idleAfter: idleAfterNumber,
      executeImmediately: executeImmediatelyBoolean,
      timezoneUTC,
      followUpCronExpression,
      followUpQuantity: parseInt(followUpQuantity, 10) || undefined,
      followUpAfter: parseInt(followUpAfter, 10) || undefined,
      followUpWorkflowId,
      isDefault: false, // Default value, can be changed later
    };

    const { customerId } = request['user'];
    const portfolio = await this.portfolioUseCase.create(createPortfolioDto, file, customerId);
    return {
      statusCode: 201,
      data: portfolio,
    };
  }

  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Create a new portfolio without file import',
    description: `
    This endpoint creates a new portfolio without importing data from a file.

    **Customer Preferences Integration:**
    - If customer preferences are configured, the portfolio will automatically use the customer's default workflow ID if not explicitly provided
    - Follow-up settings (workflow, cron expression, quantity, interval) will be applied from customer preferences
    - Timezone settings from customer preferences will be considered for scheduling
    - If customer preferences are not available or cannot be retrieved, the system will use the explicitly provided values or defaults
    `,
  })
  @ApiBody({
    type: PortfolioDto,
    description: 'Portfolio data to create',
    examples: {
      withWorkflowId: {
        summary: 'Portfolio with explicit workflow ID',
        value: {
          name: 'My Portfolio',
          workflowId: '43e3ceed-58da-4ff2-bddf-67cb79d4433f',
          workExpression: '0 9 * * 1-5',
          communicationChannel: 'WHATSAPPSELFHOSTED',
          idleAfter: 10,
          executeImmediately: false,
        },
      },
      withPreferences: {
        summary: 'Portfolio using customer preferences (workflowId optional)',
        value: {
          name: 'My Portfolio',
          workExpression: '0 9 * * 1-5',
          communicationChannel: 'WHATSAPPSELFHOSTED',
          idleAfter: 10,
          executeImmediately: false,
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The portfolio was created successfully',
    type: ResponsePortfolioDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid request body or missing required fields',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid credentials',
  })
  @Post('')
  @Version('1')
  async createPortfolio(
    @Body() createPortfolioDto: PortfolioDto,
    @Req() request: Request,
  ): Promise<any> {
    const { customerId } = request['user'];
    const portfolio = await this.portfolioUseCase.createPortfolio(createPortfolioDto, customerId);
    return {
      statusCode: 201,
      data: portfolio,
    };
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all portfolios with optional deal value filtering' })
  @ApiQuery({
    name: 'startDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-01-01T00:00:00.000Z',
    description: 'Start date for deal value filtering',
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-12-31T23:59:59.999Z',
    description: 'End date for deal value filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved portfolios with deal value metrics',
    type: [ResponsePortfolioDto],
  })
  @Get()
  @Version('1')
  async findAll(
    @Req() request: Request,
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
  ): Promise<any> {
    const { customerId } = request['user'];
    const portfolios = await this.portfolioUseCase.findAll(customerId, startDate, endDate);

    return {
      statusCode: 200,
      data: portfolios,
    };
  }

  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Search and paginate portfolios',
    description:
      'Search portfolios by name with pagination support and optional deal value filtering. Returns paginated results with metadata.',
  })
  @ApiQuery({
    name: 'name',
    type: String,
    required: false,
    example: 'test portfolio',
    description: 'Search portfolios by name (case-insensitive partial match)',
  })
  @ApiQuery({
    name: 'page',
    type: Number,
    required: false,
    example: 1,
    description: 'Page number for pagination (starts from 1)',
  })
  @ApiQuery({
    name: 'limit',
    type: Number,
    required: false,
    example: 10,
    description: 'Number of items per page (max 100)',
  })
  @ApiQuery({
    name: 'startDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-01-01T00:00:00.000Z',
    description: 'Start date for deal value filtering',
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-12-31T23:59:59.999Z',
    description: 'End date for deal value filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved paginated portfolios with search and deal value metrics',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 200 },
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/ResponsePortfolioDto' },
        },
        total: { type: 'number', example: 50 },
        limit: { type: 'number', example: 10 },
        page: { type: 'number', example: 1 },
        totalPages: { type: 'number', example: 5 },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid query parameters',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid credentials',
  })
  @Get('search')
  @Version('1')
  async searchPaginated(
    @Req() request: Request,
    @Query() query: PortfolioSearchQueryDto,
  ): Promise<any> {
    const { customerId } = request['user'];
    const { name, page, limit, startDate, endDate } = query;

    const paginatedResult = await this.portfolioUseCase.findAllWithSearchAndPagination(
      customerId,
      name,
      page || 1,
      limit || 10,
      startDate,
      endDate,
    );

    return {
      statusCode: 200,
      data: paginatedResult.data,
      total: paginatedResult.total,
      limit: paginatedResult.limit,
      page: paginatedResult.page,
      totalPages: paginatedResult.totalPages,
    };
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get portfolio by ID with optional deal value filtering' })
  @ApiQuery({
    name: 'startDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-01-01T00:00:00.000Z',
    description: 'Start date for deal value filtering',
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    format: 'date-time',
    required: false,
    example: '2024-12-31T23:59:59.999Z',
    description: 'End date for deal value filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved portfolio with deal value metrics',
    type: ResponsePortfolioDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Portfolio not found',
  })
  @Get('/:portfolioId')
  @Version('1')
  async findById(
    @Param('portfolioId') portfolioId: string,
    @Req() request: Request,
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
  ): Promise<any> {
    const customerId = request['user'].customerId;
    const portfolio = await this.portfolioUseCase.findById(
      portfolioId,
      customerId,
      startDate,
      endDate,
    );

    return {
      statusCode: 200,
      data: portfolio,
    };
  }

  @ApiOperation({ summary: 'Get all portfolio performance' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved all portfolio performance',
  })
  @Get('/performance/all')
  @Version('1')
  async findAllPortfolioPerformance(@Req() request: Request): Promise<any> {
    const customerId = request['user'].customerId;
    const portfolioPerformance = await this.portfolioUseCase.findAllPerformance(customerId);

    return {
      statusCode: 200,
      data: portfolioPerformance,
    };
  }

  @ApiOperation({ summary: 'Get portfolio performance by ID' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved portfolio performance by ID',
  })
  @ApiResponse({
    status: 404,
    description: 'Portfolio not found',
  })
  @Get('/:portfolioId/performance')
  @Version('1')
  async findPortfolioPerformanceById(
    @Param('portfolioId') portfolioId: string,
    @Req() request: Request,
  ): Promise<any> {
    const customerId = request['user'].customerId;
    const portfolioPerformance = await this.portfolioUseCase.findPerformanceById(
      portfolioId,
      customerId,
    );

    return {
      statusCode: 200,
      data: portfolioPerformance,
    };
  }

  @ApiBearerAuth()
  @Get('/:portfolioId/download')
  @Version('1')
  async downloadPortfolioOriginalFile(
    @Param('portfolioId') portfolioId: string,
    @Req() request: Request,
  ): Promise<StreamableFile> {
    const customerId = request['user'].customerId;

    const fileStream = await this.portfolioUseCase.getOriginalFileStream(customerId, portfolioId);

    return new StreamableFile(fileStream, {
      type: 'text/csv',
      disposition: 'attachment; filename="data.csv"',
    });
  }

  @Get('/:portfolioId/download/import-errors')
  @Version('1')
  async downloadPortfolioImportErrosFile(
    @Param('portfolioId') portfolioId: string,
  ): Promise<StreamableFile> {
    const fileStream = await this.portfolioUseCase.generateImportErrosFileStream(portfolioId);

    return new StreamableFile(fileStream, {
      type: 'text/csv',
      disposition: 'attachment; filename="data.csv"',
    });
  }

  @Put('/:portfolioId')
  @Version('1')
  async update(
    @Param('portfolioId') portfolioId: string,
    @Body() updatePortfolioDto: UpdatePortfolioDto,
    @Req() request: Request,
  ): Promise<any> {
    const customerId = request['user'].customerId;
    const updatedPortfolio = await this.portfolioUseCase.update(
      portfolioId,
      customerId,
      updatePortfolioDto,
    );

    return {
      statusCode: 200,
      data: updatedPortfolio,
    };
  }

  @Put('/:portfolioId/execute')
  @Version('1')
  async executePortfolio(
    @Param('portfolioId') portfolioId: string,
    @Req() request: Request,
  ): Promise<any> {
    const customerId = request['user'].customerId;
    const updatedPortfolio = await this.portfolioUseCase.updateExecutionStatusToQueued(
      portfolioId,
      customerId,
    );

    return {
      statusCode: 200,
      data: updatedPortfolio,
    };
  }

  @Put('/:portfolioId/pause')
  @Version('1')
  async pausePortfolio(
    @Param('portfolioId') portfolioId: string,
    @Req() request: Request,
  ): Promise<any> {
    const customerId = request['user'].customerId;
    const updatedPortfolio = await this.portfolioUseCase.updateExecutionStatusToPaused(
      portfolioId,
      customerId,
    );

    return {
      statusCode: 200,
      data: updatedPortfolio,
    };
  }

  @Put('/:portfolioId/cancel')
  @Version('1')
  async cancelPortfolio(
    @Param('portfolioId') portfolioId: string,
    @Req() request: Request,
  ): Promise<any> {
    const customerId = request['user'].customerId;
    const updatedPortfolio = await this.portfolioUseCase.updateExecutionStatusToCancelled(
      portfolioId,
      customerId,
    );

    return {
      statusCode: 200,
      data: updatedPortfolio,
    };
  }

  @Delete('/:portfolioId')
  @Version('1')
  async deleteById(
    @Param('portfolioId') portfolioId: string,
    @Req() request: Request,
  ): Promise<any> {
    const customerId = request['user'].customerId;
    const portfolio = await this.portfolioUseCase.delete(portfolioId, customerId);

    return {
      statusCode: 200,
      data: portfolio,
    };
  }

  @Get('/:portfolioId/export/available-columns')
  @Version('1')
  async getAvailableColumnsByPortfolioId(@Param('portfolioId') portfolioId: string): Promise<any> {
    const availableColumns = await this.portfolioUseCase.getAvailableColumns(portfolioId);

    return {
      statusCode: 200,
      data: availableColumns,
    };
  }

  @Post('/:portfolioId/export')
  @Version('1')
  async exportPortfolio(
    @Param('portfolioId') portfolioId: string,
    @Body() selectedColumnsDto: SelectedColumnsDto,
  ): Promise<StreamableFile> {
    const fileStream = await this.portfolioUseCase.exportPortfolioItemsToCsv(
      portfolioId,
      selectedColumnsDto.selectedColumns || [],
    );

    return new StreamableFile(fileStream, {
      type: 'text/csv',
      disposition: 'attachment; filename="data.csv"',
    });
  }
}
