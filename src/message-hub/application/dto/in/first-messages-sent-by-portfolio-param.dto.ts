import { IsUUID, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class FirstMessagesSentByPortfolioParamDto {
  @ApiProperty({
    description: 'UUID of the portfolio to get first messages sent metrics for',
    example: '30b0e1fb-64c7-4519-93aa-4eb5dd33087e',
    type: String,
    format: 'uuid',
  })
  @IsUUID('4', { message: 'portfolioId must be a valid UUID' })
  @IsNotEmpty({ message: 'portfolioId is required' })
  readonly portfolioId: string;
}
