import { IsString, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class FirstMessagesSentByPortfolioQueryDto {
  @ApiProperty({
    description: 'Start date for filtering messages (YYYY-MM-DD format)',
    example: '2024-06-01',
    type: String,
    format: 'date',
  })
  @IsString({ message: 'dateStart must be a string' })
  @IsNotEmpty({ message: 'dateStart is required' })
  readonly dateStart: string;

  @ApiProperty({
    description: 'End date for filtering messages (YYYY-MM-DD format)',
    example: '2024-06-12',
    type: String,
    format: 'date',
  })
  @IsString({ message: 'dateEnd must be a string' })
  @IsNotEmpty({ message: 'dateEnd is required' })
  readonly dateEnd: string;
}
