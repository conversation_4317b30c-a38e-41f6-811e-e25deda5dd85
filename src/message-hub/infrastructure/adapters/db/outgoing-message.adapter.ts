import { Injectable } from '@nestjs/common';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { OutgoingMessageEntity } from '@message-hub/domain/entities/outgoing-message.entity';
import { OutgoingMessagePort } from '@message-hub/infrastructure/ports/db/outgoing-message.port';
import { Prisma } from '@prisma/client';
import { randomUUID } from 'crypto';
import { CommunicationChannel } from '@common/enums';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { logger } from '@edutalent/commons-sdk';

@Injectable()
export class OutgoingMessageAdapter
  extends PrismaCommonAdapter<OutgoingMessageEntity>
  implements OutgoingMessagePort {
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'outgoingMessage');
  }

  async insertOutgoingMessage(
    {
      customerId,
      from,
      to,
      messageType,
      message,
      channel,
      status,
      isFirstMessage,
      apiUrl,
      fileUrl,
    }: {
      customerId: string;
      from: string;
      to: string;
      messageType: string;
      message: string;
      channel: string;
      status: string;
      isFirstMessage: boolean;
      apiUrl: string;
      fileUrl: string;
    },
    randomDelay: number,
  ): Promise<void> {
    try {
      return this.prisma.client.$transaction(async tx => {
        // Get customer phone to check daily limit
        const customerPhone = await tx.customerPhone.findFirst({
          where: { phoneNumber: from, communicationChannel: channel },
        });

        if (!customerPhone) {
          throw new Error(`Customer phone not found for number: ${from} and channel: ${channel}`);
        }

        // Get the latest scheduled message time
        const result = (await tx.$queryRaw(Prisma.sql`
          SELECT "time_to_go" as "maxTimeToGo"
          FROM "message_hub"."outgoing_message"
          WHERE communication_channel = ${channel}
            AND "from" = ${from}
            AND "status" = 'ACTIVE'
          FOR UPDATE
        `)) as { maxTimeToGo: Date }[];

        let newTimeToGo: Date;

        const maxTimeToGo = result[0]?.maxTimeToGo ? new Date(result[0].maxTimeToGo) : null;

        const now = new Date(); // current time in UTC

        const lastTimeToGo =
          maxTimeToGo && maxTimeToGo.getTime() > now.getTime() ? maxTimeToGo : now;

        const delayInSeconds = Number(randomDelay);
        const delayInMs = delayInSeconds * 1000;

        // Helper functions for business hours and scheduling
        const convertToBrazilTime = (utcDate: Date): Date => {
          // Brazil is UTC-3 (standard time)
          const brazilOffset = -3 * 60; // -3 hours in minutes
          const utcTime = utcDate.getTime() + (utcDate.getTimezoneOffset() * 60000);
          return new Date(utcTime + (brazilOffset * 60000));
        };

        const isBusinessHours = (utcDate: Date): boolean => {
          const brazilDate = convertToBrazilTime(utcDate);
          const day = brazilDate.getDay();
          const hours = brazilDate.getHours();

          // Sunday (0) - no messages
          if (day === 0) {
            return false;
          }

          // Monday to Friday (1-5): 8:00 AM to 7:59 PM
          if (day >= 1 && day <= 5) {
            return hours >= 8 && hours < 20;
          }

          // Saturday (6): 8:00 AM to 2:00 PM
          if (day === 6) {
            return hours >= 8 && hours < 14;
          }

          return false;
        };

        const getNextBusinessDay = (utcDate: Date): Date => {
          const nextDay = new Date(utcDate);
          nextDay.setDate(nextDay.getDate() + 1);

          // Skip to next business day using Brazil timezone
          while (!isBusinessHours(nextDay)) {
            nextDay.setDate(nextDay.getDate() + 1);
          }

          // Set to 8:00 AM Brazil time (which will be stored as UTC)
          const brazilDate = convertToBrazilTime(nextDay);
          brazilDate.setHours(8, 0, 0, 0);

          // Convert back to UTC for storage
          const utcResult = new Date(brazilDate.getTime() + (3 * 60 * 60 * 1000)); // Add 3 hours to convert Brazil time to UTC
          return utcResult;
        };

        const countScheduledMessagesForDay = async (date: Date): Promise<number> => {
          const startOfDay = new Date(date);
          startOfDay.setHours(0, 0, 0, 0);
          const endOfDay = new Date(date);
          endOfDay.setHours(23, 59, 59, 999);

          const [count] = (await tx.$queryRaw(Prisma.sql`
            SELECT COUNT(*) as count
            FROM "message_hub"."outgoing_message"
            WHERE "from" = ${from}
              AND "communication_channel" = ${channel}
              AND "is_first_message" = true
              AND "status" = 'ACTIVE'
              AND "time_to_go" >= ${startOfDay.toISOString()}::timestamp
              AND "time_to_go" <= ${endOfDay.toISOString()}::timestamp
          `)) as [{ count: number }];

          return count.count;
        };

        if (!isFirstMessage || process.env.NODE_ENV === 'development') {
          // For non-first messages or development, schedule immediately with delay
          newTimeToGo = new Date(now.getTime() + delayInMs);

          logger.info(
            `InsertOutgoingMessage DEBUG   UTC: now: ${now.toISOString()}, maxTimeToGo: ${maxTimeToGo?.toISOString()}, lastTimeToGo: ${lastTimeToGo.toISOString()}, delayInMs: ${delayInMs}, newTimeToGo: ${newTimeToGo.toISOString()}`,
          );
        } else {
          // For first messages, apply business rules
          let candidateTimeToGo = new Date(lastTimeToGo.getTime() + delayInMs);

          logger.info(
            `InsertOutgoingMessage DEBUG   UTC: now: ${now.toISOString()}, maxTimeToGo: ${maxTimeToGo?.toISOString()}, lastTimeToGo: ${lastTimeToGo.toISOString()}, delayInMs: ${delayInMs}, candidateTimeToGo: ${candidateTimeToGo.toISOString()}`,
          );

          // Check daily limit for the candidate day BEFORE finalizing the schedule
          let currentDayCount = await countScheduledMessagesForDay(candidateTimeToGo);

          // If candidate time is outside business hours or daily limit exceeded, find next available slot
          while (!isBusinessHours(candidateTimeToGo) || currentDayCount >= customerPhone.dailyLimit) {
            candidateTimeToGo = getNextBusinessDay(candidateTimeToGo);
            currentDayCount = await countScheduledMessagesForDay(candidateTimeToGo);
          }

          // Now find the latest message for the selected day to maintain proper sequencing
          const startOfDay = new Date(candidateTimeToGo);
          startOfDay.setHours(0, 0, 0, 0);
          const endOfDay = new Date(candidateTimeToGo);
          endOfDay.setHours(23, 59, 59, 999);

          const latestMessage = (await tx.$queryRaw(Prisma.sql`
            SELECT "time_to_go" as "maxTimeToGo"
            FROM "message_hub"."outgoing_message"
            WHERE "sent" = false
              AND communication_channel = ${channel}
              AND "from" = ${from}
              AND "status" = 'ACTIVE'
              AND "time_to_go" >= ${startOfDay.toISOString()}::timestamp
              AND "time_to_go" <= ${endOfDay.toISOString()}::timestamp
            ORDER BY "time_to_go" DESC LIMIT 1
            FOR UPDATE
          `)) as { maxTimeToGo: Date }[];

          if (!latestMessage || latestMessage.length === 0) {
            // First message of the day: schedule at 8:00 AM Brazil time
            const brazilDate = convertToBrazilTime(candidateTimeToGo);
            brazilDate.setHours(8, 0, 0, 0);
            // Convert back to UTC for storage
            newTimeToGo = new Date(brazilDate.getTime() + (3 * 60 * 60 * 1000));
          } else {
            // Subsequent messages: add random delay to the latest message time
            const latestTime = new Date(latestMessage[0].maxTimeToGo.getTime() + Number(randomDelay) * 1000);

            // Ensure we don't go outside business hours
            if (isBusinessHours(latestTime)) {
              newTimeToGo = latestTime;
            } else {
              // If adding delay would go outside business hours, schedule for next business day
              newTimeToGo = getNextBusinessDay(latestTime);
            }
          }
        }

        // Final safety check: ensure we never schedule messages in the past
        if (newTimeToGo.getTime() <= now.getTime()) {
          logger.warn(
            `InsertOutgoingMessage: Attempted to schedule message in the past. Adjusting to now + delay. Original: ${newTimeToGo.toISOString()}, Now: ${now.toISOString()}`,
          );
          newTimeToGo = new Date(now.getTime() + delayInMs);
        }

        logger.info(
          `InsertOutgoingMessage FINAL   UTC: newTimeToGo: ${newTimeToGo.toISOString()}, isFirstMessage: ${isFirstMessage}`,
        );

        await tx.$executeRaw(
          Prisma.sql`
            INSERT INTO "message_hub"."outgoing_message" ("id", "customer_id", "to", "from", "message_type",
                                                          "message",
                                                          "communication_channel", "time_to_go", "sent",
                                                          "status", "created_at", "updated_at",
                                                          "api_url", "file_url", "is_first_message")
            VALUES (${randomUUID()}::uuid, ${customerId}::uuid, ${to}, ${from}, ${messageType}, ${message},
                    ${channel}, ${newTimeToGo.toISOString()}::timestamp, false,
                    ${status}, ${now}::timestamp, ${now}::timestamp, ${apiUrl}, ${fileUrl}, ${isFirstMessage})
          `,
        );
      });
    } catch (error) {
      throw new Error(`Error inserting outgoing message: ${error}`);
    }
  }

  async processOutgoingMessage(
    fromNumber: string,
    channel: CommunicationChannel,
    sendMessage: (to: string, message: string, apiUrl: string) => Promise<void>,
    sendMessageWithFile: (
      to: string,
      message: string,
      apiUrl: string,
      fileUrl: string,
      fileType: string,
    ) => Promise<void>,
    sendMessageSMS: (from: string, to: string, text: string, apiUrl: string) => Promise<void>,
  ): Promise<void> {
    const now = new Date();
    return this.prisma.client.$transaction(
      async tx => {
        const messages = (await tx.$queryRaw(Prisma.sql`
          SELECT "id",
                 "from",
                 "to",
                 "message",
                 "time_to_go"            AS "timeToGo",
                 "communication_channel" AS "channel",
                 "api_url"               AS "apiUrl",
                 "message_type"          AS "messageType",
                 "file_url"              AS "fileUrl"
          FROM "message_hub"."outgoing_message"
          WHERE "sent" = false
            AND "time_to_go" <= ${now}::timestamp
            AND "from" = ${fromNumber}
            AND "communication_channel" = ${channel}
            AND "status" = 'ACTIVE'
          ORDER BY "time_to_go" ASC LIMIT 1
              FOR
          UPDATE SKIP LOCKED
        `)) as {
          id: string;
          from: string;
          to: string;
          message: string;
          timeToGo: Date;
          channel: string;
          apiUrl: string;
          messageType: string;
          fileUrl?: string;
        }[];

        if (!messages || messages.length === 0) {
          return;
        }

        const customerPhone = await tx.customerPhone.findFirst({
          where: { phoneNumber: fromNumber, communicationChannel: channel },
        });

        if (!customerPhone) {
          throw new Error(
            `Customer phone not found for number: ${fromNumber} and channel: ${channel}`,
          );
        }

        const maxOutgoingDelay = customerPhone.outgoingMaxDelay;

        for (const message of messages) {
          const startSendingDate = new Date();
          try {
            if (message.channel === CommunicationChannel.WHATSAPPSELFHOSTED) {
              if (message.fileUrl) {
                await sendMessageWithFile(
                  message.to,
                  message.message,
                  message.apiUrl,
                  message.fileUrl,
                  message.messageType,
                );
              } else {
                await sendMessage(message.to, message.message, message.apiUrl);
              }
            } else if (message.channel === CommunicationChannel.SMS_VONAGE) {
              await sendMessageSMS(message.from, message.to, message.message, message.apiUrl);
            } else {
              throw new BusinessException(
                'Outgoing-Message-Adapter',
                `Channel ${message.channel} not supported for number: ${fromNumber}`,
                BusinessExceptionStatus.INVALID_INPUT,
              );
            }

            await tx.$executeRaw(
              Prisma.sql`
                UPDATE "message_hub"."outgoing_message"
                SET "sent"    = true,
                    "sent_at" = ${now}::timestamp,
                WHERE "id" = ${message.id}::uuid
              `,
            );

            const finishedSendingDate = new Date();
            logger.info(
              `Message sent successfully from phone: ${customerPhone.phoneNumber} to: ${message.to
              }. Took: ${finishedSendingDate.getTime() - startSendingDate.getTime()
              }ms. Message: ${JSON.stringify(message)}`,
            );
          } catch (error) {
            await tx.$executeRaw(
              Prisma.sql`
                UPDATE "message_hub"."outgoing_message"
                SET "status" = 'DELETED',
              "updated_at" = NOW(),
              "sent" = true,
              "sent_at" = NOW()
                WHERE "id" = ${message.id}:: uuid
              `,
            );

            const finishedSendingDate = new Date();
            logger.error(
              `Failed to send message from phone: ${customerPhone.phoneNumber} to: ${message.to
              }.Took: ${finishedSendingDate.getTime() - startSendingDate.getTime()
              }ms.Message: ${JSON.stringify(message)}.Error: ${error.message}`,
            );
          }
        }
      },
      {
        isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
        maxWait: 60000,
        timeout: 60000,
      },
    );
  }

  async getPendingOutgoingMessage(
    customerId: string,
    channel: CommunicationChannel,
    to: string,
  ): Promise<OutgoingMessageEntity[]> {
    return this.prisma.client.$transaction(
      async tx => {
        const messages = (await tx.$queryRaw(Prisma.sql`
          SELECT "id",
              "from",
              "to",
              "message",
              "time_to_go"            AS "timeToGo",
              "communication_channel" AS "channel",
              "api_url"               AS "apiUrl",
              "message_type"          AS "messageType",
              "file_url"              AS "fileUrl"
          FROM "message_hub"."outgoing_message"
          WHERE "sent" = false
            AND "customer_id" = ${Prisma.sql`${customerId}::uuid`}
            AND "to" = ${to}
            AND "communication_channel" = ${channel}
          ORDER BY "time_to_go" ASC
            FOR
              UPDATE SKIP LOCKED
              `)) as OutgoingMessageEntity[];

        if (!messages || messages.length === 0) {
          // logger.info(
          //   `No pending messages to send for customer: ${ customerId } and channel: ${ channel }...`,
          // );
          return [];
        }

        const customerPhone = await tx.customerPhone.findFirst({
          where: { customerId: customerId, communicationChannel: channel },
        });

        if (!customerPhone) {
          throw new Error(
            `Customer phone not found for customer: ${customerId} and channel: ${channel} `,
          );
        }

        await tx.$executeRaw(
          Prisma.sql`
            UPDATE message_hub.outgoing_message om
            SET sent = true,
      sent_at = NOW(),
      updated_at = NOW()
            WHERE om.id IN(${Prisma.join(
            messages.map(message => Prisma.sql`${message.id}::uuid`),
          )
            });
    `,
        );

        return messages;
      },
      {
        isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
        maxWait: 60000,
        timeout: 60000,
      },
    );
  }

  async getTimestampFromDatabase(): Promise<{ now: Date; nowToString: string }> {
    return this.prisma.client.$transaction(async tx => {
      const [currentDatabaseTime] = (await tx.$queryRaw(Prisma.sql`
            SELECT now() as now
    `)) as [{ now: Date }];
      return { now: currentDatabaseTime.now, nowToString: currentDatabaseTime.now.toString() };
    });
  }

  async getTotalFirstMessagesSentByPortfolio(
    customerId: string,
    portfolioId: string,
    dateStart: Date,
    dateEnd: Date,
  ): Promise<{ date: string; count: number; total: number }[]> {
    const results = await this.prisma.client.$queryRaw<{ date: string; count: number }[]>(
      Prisma.sql`
        SELECT TO_CHAR(DATE(om.sent_at), 'YYYY-MM-DD') as date,
      COUNT(*) as count
        FROM message_hub.outgoing_message om
        INNER JOIN business_base.portfolio_item pi ON om.to = pi.phone_number
        WHERE om.customer_id = ${customerId}:: uuid
          AND pi.portfolio_id = ${portfolioId}:: uuid
          AND om.sent = true
          AND om.is_first_message = true
          AND om.sent_at >= ${dateStart.toISOString()}:: timestamp
          AND om.sent_at <= ${dateEnd.toISOString()}:: timestamp
          AND pi.status = 'ACTIVE'
          AND om.status != 'DELETED'
        GROUP BY DATE(om.sent_at)
        ORDER BY DATE(om.sent_at) ASC
      `,
    );

    // Calculate total count
    const total = results.reduce((sum, row) => sum + Number(row.count), 0);

    // Return results with total included in each row for consistency
    return results.map(row => ({
      date: row.date,
      count: Number(row.count),
      total,
    }));
  }
}
