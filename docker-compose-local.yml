services:
  postgres:
    container_name: transcendence_local_db
    image: pgvector/pgvector:pg15
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password123
      POSTGRES_DB: transcendence_db
    ports:
      - '5442:5432'
    volumes:
      - transcendence_postgres_data:/var/lib/postgresql/data
    networks:
      - transcendence_network

  opensearch:
    image: opensearchproject/opensearch:2.11.1
    environment:
      - discovery.type=single-node
      - plugins.security.disabled=true
      - OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m
    ports:
      - '9200:9200'
    networks:
      - transcendence_network

  localstack:
    container_name: localstack_local_infra
    image: localstack/localstack
    ports:
      - '4566:4566'
      - '4571:4571'
      - '8056:8080'
    environment:
      - SERVICES=dynamodb,logs,s3,sqs,cloudwatch
      - DEBUG=1
    volumes:
      - localstack_data:/var/lib/localstack
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - transcendence_network

volumes:
  transcendence_postgres_data:
  localstack_data:

networks:
  transcendence_network:
    name: transcendence_network
    external: true
