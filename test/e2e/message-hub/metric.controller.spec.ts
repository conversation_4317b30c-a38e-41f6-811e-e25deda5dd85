import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { getAuthCredentials } from 'test/helpers/authenticated-user';

describe('Metrics controller (e2e)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    app = global.__NEST_APP__;
    const { accessToken } = await getAuthCredentials(app, '<EMAIL>', 'password123');
    authToken = accessToken;
  });

  describe('GET /v1/message-hub/metrics/answer-messages-sent', () => {
    it('should return total messages sent for the given date range', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-01-31';

      const response = await request(app.getHttpServer())
        .get('/api/v1/message-hub/metrics/answer-messages-sent')
        .query({
          dateStart,
          dateEnd,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toHaveProperty('totalAnswerMessagesSent');
    });

    it('should return 401 when no auth token is provided', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-01-31';

      await request(app.getHttpServer())
        .get('/api/v1/message-hub/metrics/answer-messages-sent')
        .query({
          dateStart,
          dateEnd,
        })
        .expect(401);
    });
  });

  describe('GET /v1/message-hub/metrics/first-messages-sent', () => {
    it('should return total messages sent for the given date range', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-01-31';

      const response = await request(app.getHttpServer())
        .get('/api/v1/message-hub/metrics/first-messages-sent')
        .query({
          dateStart,
          dateEnd,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toHaveProperty('totalFirstMessagesSent');
    });

    it('should return 401 when no auth token is provided', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-01-31';

      await request(app.getHttpServer())
        .get('/api/v1/message-hub/metrics/first-messages-sent')
        .query({
          dateStart,
          dateEnd,
        })
        .expect(401);
    });
  });

  describe('GET /v1/message-hub/metrics/messages-received', () => {
    it('should return total messages received for the given date range', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-01-31';

      const response = await request(app.getHttpServer())
        .get('/api/v1/message-hub/metrics/messages-received')
        .query({
          dateStart,
          dateEnd,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toHaveProperty('totalMessagesReceived');
    });

    it('should return 401 when no auth token is provided', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-01-31';

      await request(app.getHttpServer())
        .get('/api/v1/message-hub/metrics/messages-received')
        .query({
          dateStart,
          dateEnd,
        })
        .expect(401);
    });
  });

  describe('GET /v1/message-hub/metrics/first-messages-sent/:portfolioId', () => {
    let testPortfolioId: string;

    beforeAll(async () => {
      // Use existing seeded portfolio ID from test data
      testPortfolioId = '30b0e1fb-64c7-4519-93aa-4eb5dd33087e';
    });

    it('should return total first messages sent for a specific portfolio', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-12-31';

      const response = await request(app.getHttpServer())
        .get(`/api/v1/message-hub/metrics/first-messages-sent/${testPortfolioId}`)
        .query({
          dateStart,
          dateEnd,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toHaveProperty('totalFirstMessagesSent');
      expect(response.body.data.totalFirstMessagesSent).toHaveProperty('total');
      expect(response.body.data.totalFirstMessagesSent).toHaveProperty('dailyTotals');
      expect(typeof response.body.data.totalFirstMessagesSent.total).toBe('number');
      expect(typeof response.body.data.totalFirstMessagesSent.dailyTotals).toBe('object');
    });

    it('should return zero counts for portfolio with no messages', async () => {
      const nonExistentPortfolioId = '12345678-1234-4567-8901-123456789012';
      const dateStart = '2024-01-01';
      const dateEnd = '2024-12-31';

      const response = await request(app.getHttpServer())
        .get(`/api/v1/message-hub/metrics/first-messages-sent/${nonExistentPortfolioId}`)
        .query({
          dateStart,
          dateEnd,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data.totalFirstMessagesSent.total).toBe(0);
      expect(response.body.data.totalFirstMessagesSent.dailyTotals).toEqual({});
    });

    it('should return correct date format in dailyTotals (YYYY-MM-DD)', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-12-31';

      const response = await request(app.getHttpServer())
        .get(`/api/v1/message-hub/metrics/first-messages-sent/${testPortfolioId}`)
        .query({
          dateStart,
          dateEnd,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const dailyTotals = response.body.data.totalFirstMessagesSent.dailyTotals;

      // Check that all date keys are in YYYY-MM-DD format
      Object.keys(dailyTotals).forEach(dateKey => {
        expect(dateKey).toMatch(/^\d{4}-\d{2}-\d{2}$/);
        expect(typeof dailyTotals[dateKey]).toBe('number');
        expect(dailyTotals[dateKey]).toBeGreaterThanOrEqual(0);
      });
    });

    it('should filter messages by date range correctly', async () => {
      const dateStart = '2024-06-01';
      const dateEnd = '2024-06-30';

      const response = await request(app.getHttpServer())
        .get(`/api/v1/message-hub/metrics/first-messages-sent/${testPortfolioId}`)
        .query({
          dateStart,
          dateEnd,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const dailyTotals = response.body.data.totalFirstMessagesSent.dailyTotals;

      // All dates should be within the specified range
      Object.keys(dailyTotals).forEach(dateKey => {
        expect(dateKey >= dateStart).toBe(true);
        expect(dateKey <= dateEnd).toBe(true);
      });
    });

    it('should return 401 when no auth token is provided', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-12-31';

      await request(app.getHttpServer())
        .get(`/api/v1/message-hub/metrics/first-messages-sent/${testPortfolioId}`)
        .query({
          dateStart,
          dateEnd,
        })
        .expect(401);
    });

    it('should return 400 when portfolioId is invalid UUID format', async () => {
      const invalidPortfolioId = 'invalid-uuid';
      const dateStart = '2024-01-01';
      const dateEnd = '2024-12-31';

      await request(app.getHttpServer())
        .get(`/api/v1/message-hub/metrics/first-messages-sent/${invalidPortfolioId}`)
        .query({
          dateStart,
          dateEnd,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);
    });

    it('should return 400 when dateStart is missing', async () => {
      const dateEnd = '2024-12-31';

      await request(app.getHttpServer())
        .get(`/api/v1/message-hub/metrics/first-messages-sent/${testPortfolioId}`)
        .query({
          dateEnd,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);
    });

    it('should return 400 when dateEnd is missing', async () => {
      const dateStart = '2024-01-01';

      await request(app.getHttpServer())
        .get(`/api/v1/message-hub/metrics/first-messages-sent/${testPortfolioId}`)
        .query({
          dateStart,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);
    });

    it('should handle edge case when dateStart equals dateEnd', async () => {
      const singleDate = '2024-07-15';

      const response = await request(app.getHttpServer())
        .get(`/api/v1/message-hub/metrics/first-messages-sent/${testPortfolioId}`)
        .query({
          dateStart: singleDate,
          dateEnd: singleDate,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data.totalFirstMessagesSent).toHaveProperty('total');
      expect(response.body.data.totalFirstMessagesSent).toHaveProperty('dailyTotals');
    });

    it('should only count first messages (not follow-up messages)', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-12-31';

      const response = await request(app.getHttpServer())
        .get(`/api/v1/message-hub/metrics/first-messages-sent/${testPortfolioId}`)
        .query({
          dateStart,
          dateEnd,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // This test verifies the endpoint only counts first messages
      // The actual verification would be done by checking the database query
      // but we can at least verify the response structure is correct
      expect(response.body.data.totalFirstMessagesSent).toHaveProperty('total');
      expect(response.body.data.totalFirstMessagesSent).toHaveProperty('dailyTotals');
      expect(typeof response.body.data.totalFirstMessagesSent.total).toBe('number');
    });

    it('should only count sent messages (not pending or failed)', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-12-31';

      const response = await request(app.getHttpServer())
        .get(`/api/v1/message-hub/metrics/first-messages-sent/${testPortfolioId}`)
        .query({
          dateStart,
          dateEnd,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // This test verifies the endpoint only counts sent messages
      // The actual verification would be done by checking the database query
      // but we can at least verify the response structure is correct
      expect(response.body.data.totalFirstMessagesSent).toHaveProperty('total');
      expect(response.body.data.totalFirstMessagesSent).toHaveProperty('dailyTotals');
      expect(typeof response.body.data.totalFirstMessagesSent.total).toBe('number');
    });

    it('should maintain consistency between total and dailyTotals sum', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-12-31';

      const response = await request(app.getHttpServer())
        .get(`/api/v1/message-hub/metrics/first-messages-sent/${testPortfolioId}`)
        .query({
          dateStart,
          dateEnd,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const { total, dailyTotals } = response.body.data.totalFirstMessagesSent;
      const dailySum = Object.values(dailyTotals).reduce(
        (sum: number, count: number) => sum + count,
        0,
      );

      expect(total).toBe(dailySum);
    });
  });
});
