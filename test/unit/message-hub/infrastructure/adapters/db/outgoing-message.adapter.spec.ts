import { Test, TestingModule } from '@nestjs/testing';
import { OutgoingMessageAdapter } from '@message-hub/infrastructure/adapters/db/outgoing-message.adapter';
import { PrismaService } from '@common/prisma/prisma.service';
import { CommunicationChannel } from '@common/enums';

describe('OutgoingMessageAdapter', () => {
  let adapter: OutgoingMessageAdapter;

  const mockPrismaService = {
    client: {
      $transaction: jest.fn(),
      customerPhone: {
        findFirst: jest.fn(),
      },
    },
  };

  // Use a fixed date for all tests - Wednesday 2025-08-01 at 11:00 AM UTC (8:00 AM Brazil time)
  const FIXED_DATE = new Date('2025-08-01T14:00:00.000Z'); // 11:00 AM Brazil time

  beforeAll(() => {
    jest.useFakeTimers();
    jest.setSystemTime(FIXED_DATE);
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OutgoingMessageAdapter,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    adapter = module.get<OutgoingMessageAdapter>(OutgoingMessageAdapter);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  describe('insertOutgoingMessage', () => {
    const baseMessageData = {
      customerId: '4cd6d515-2604-4c2c-adad-435acbef1f5c',
      from: '5511915183129',
      to: '5511999999991',
      messageType: 'TEXT',
      message: 'Test message',
      channel: CommunicationChannel.WHATSAPPSELFHOSTED,
      status: 'ACTIVE',
      isFirstMessage: false,
      apiUrl: 'http://localhost:3012',
      fileUrl: null,
    };

    const randomDelay = 60;
    const mockCustomerPhone = {
      phoneNumber: '5511915183129',
      communicationChannel: CommunicationChannel.WHATSAPPSELFHOSTED,
      dailyLimit: 20,
    };

    describe('Non-first messages and development mode', () => {
      it('should schedule non-first message immediately with delay', async () => {
        mockPrismaService.client.customerPhone.findFirst.mockResolvedValue(mockCustomerPhone);

        mockPrismaService.client.$transaction.mockImplementation(async callback => {
          const mockTx = {
            $queryRaw: jest.fn().mockResolvedValue([{ maxTimeToGo: new Date(FIXED_DATE) }]),
            $executeRaw: jest.fn(),
            customerPhone: mockPrismaService.client.customerPhone,
          };
          return callback(mockTx);
        });

        await adapter.insertOutgoingMessage(baseMessageData, randomDelay);

        expect(mockPrismaService.client.$transaction).toHaveBeenCalled();
      });

      it('should schedule first message immediately in development mode', async () => {
        const originalEnv = process.env.NODE_ENV;
        process.env.NODE_ENV = 'development';

        const firstMessageData = { ...baseMessageData, isFirstMessage: true };
        mockPrismaService.client.customerPhone.findFirst.mockResolvedValue(mockCustomerPhone);

        mockPrismaService.client.$transaction.mockImplementation(async callback => {
          const mockTx = {
            $queryRaw: jest.fn().mockResolvedValue([{ maxTimeToGo: new Date(FIXED_DATE) }]),
            $executeRaw: jest.fn(),
            customerPhone: mockPrismaService.client.customerPhone,
          };
          return callback(mockTx);
        });

        await adapter.insertOutgoingMessage(firstMessageData, randomDelay);

        expect(mockPrismaService.client.$transaction).toHaveBeenCalled();
        process.env.NODE_ENV = originalEnv;
      });
    });

    describe('Daily limit enforcement', () => {
      const firstMessageData = { ...baseMessageData, isFirstMessage: true };

      it('should schedule first message at 8:00 AM Brazil time when daily limit not exceeded', async () => {
        mockPrismaService.client.customerPhone.findFirst.mockResolvedValue(mockCustomerPhone);

        mockPrismaService.client.$transaction.mockImplementation(async callback => {
          const mockTx = {
            $queryRaw: jest
              .fn()
              .mockResolvedValueOnce([{ maxTimeToGo: new Date(FIXED_DATE) }]) // maxTimeToGo query
              .mockResolvedValueOnce([{ count: 5 }]) // daily count query - under limit of 20
              .mockResolvedValueOnce([]), // no existing messages for the day
            $executeRaw: jest.fn(),
            customerPhone: mockPrismaService.client.customerPhone,
          };
          return callback(mockTx);
        });

        await adapter.insertOutgoingMessage(firstMessageData, randomDelay);

        expect(mockPrismaService.client.$transaction).toHaveBeenCalled();
      });

      it('should schedule for next business day when daily limit exceeded', async () => {
        mockPrismaService.client.customerPhone.findFirst.mockResolvedValue(mockCustomerPhone);

        mockPrismaService.client.$transaction.mockImplementation(async callback => {
          const mockTx = {
            $queryRaw: jest
              .fn()
              .mockResolvedValueOnce([{ maxTimeToGo: new Date(FIXED_DATE) }]) // maxTimeToGo query
              .mockResolvedValueOnce([{ count: 20 }]) // daily count query - at limit
              .mockResolvedValueOnce([{ count: 0 }]) // count for next day
              .mockResolvedValueOnce([]), // no existing messages for next day
            $executeRaw: jest.fn(),
            customerPhone: mockPrismaService.client.customerPhone,
          };
          return callback(mockTx);
        });

        await adapter.insertOutgoingMessage(firstMessageData, randomDelay);

        expect(mockPrismaService.client.$transaction).toHaveBeenCalled();
      });

      it('should add delay to latest message when multiple messages scheduled same day', async () => {
        const latestMessageTime = new Date('2025-08-01T15:30:00.000Z'); // 12:30 PM Brazil time
        mockPrismaService.client.customerPhone.findFirst.mockResolvedValue(mockCustomerPhone);

        mockPrismaService.client.$transaction.mockImplementation(async callback => {
          const mockTx = {
            $queryRaw: jest
              .fn()
              .mockResolvedValueOnce([{ maxTimeToGo: new Date(FIXED_DATE) }]) // maxTimeToGo query
              .mockResolvedValueOnce([{ count: 10 }]) // daily count query - under limit
              .mockResolvedValueOnce([{ maxTimeToGo: latestMessageTime }]), // existing message for the day
            $executeRaw: jest.fn(),
            customerPhone: mockPrismaService.client.customerPhone,
          };
          return callback(mockTx);
        });

        await adapter.insertOutgoingMessage(firstMessageData, randomDelay);

        expect(mockPrismaService.client.$transaction).toHaveBeenCalled();
      });
    });

    describe('Business hours and weekend scheduling', () => {
      const firstMessageData = { ...baseMessageData, isFirstMessage: true };

      it('should handle Sunday scheduling (skip to Monday)', async () => {
        const sundayDate = new Date('2025-08-03T14:00:00.000Z'); // Sunday 11:00 AM Brazil time
        jest.setSystemTime(sundayDate);

        mockPrismaService.client.customerPhone.findFirst.mockResolvedValue(mockCustomerPhone);

        mockPrismaService.client.$transaction.mockImplementation(async callback => {
          const mockTx = {
            $queryRaw: jest
              .fn()
              .mockResolvedValueOnce([{ maxTimeToGo: sundayDate }])
              .mockResolvedValueOnce([{ count: 0 }])
              .mockResolvedValueOnce([]),
            $executeRaw: jest.fn(),
            customerPhone: mockPrismaService.client.customerPhone,
          };
          return callback(mockTx);
        });

        await adapter.insertOutgoingMessage(firstMessageData, randomDelay);

        expect(mockPrismaService.client.$transaction).toHaveBeenCalled();
        jest.setSystemTime(FIXED_DATE);
      });

      it('should handle Saturday afternoon scheduling (skip to Monday)', async () => {
        const saturdayAfternoon = new Date('2025-08-02T18:00:00.000Z'); // Saturday 3 PM Brazil time
        jest.setSystemTime(saturdayAfternoon);

        mockPrismaService.client.customerPhone.findFirst.mockResolvedValue(mockCustomerPhone);

        mockPrismaService.client.$transaction.mockImplementation(async callback => {
          const mockTx = {
            $queryRaw: jest
              .fn()
              .mockResolvedValueOnce([{ maxTimeToGo: saturdayAfternoon }])
              .mockResolvedValueOnce([{ count: 0 }])
              .mockResolvedValueOnce([]),
            $executeRaw: jest.fn(),
            customerPhone: mockPrismaService.client.customerPhone,
          };
          return callback(mockTx);
        });

        await adapter.insertOutgoingMessage(firstMessageData, randomDelay);

        expect(mockPrismaService.client.$transaction).toHaveBeenCalled();
        jest.setSystemTime(FIXED_DATE);
      });
    });

    describe('Past date prevention and timezone handling', () => {
      it('should adjust to current time + delay when scheduled time is in the past', async () => {
        const pastTime = new Date('2025-08-01T10:00:00.000Z'); // Before current FIXED_DATE

        mockPrismaService.client.customerPhone.findFirst.mockResolvedValue(mockCustomerPhone);

        mockPrismaService.client.$transaction.mockImplementation(async callback => {
          const mockTx = {
            $queryRaw: jest.fn().mockResolvedValue([{ maxTimeToGo: pastTime }]),
            $executeRaw: jest.fn(),
            customerPhone: mockPrismaService.client.customerPhone,
          };
          return callback(mockTx);
        });

        await adapter.insertOutgoingMessage(baseMessageData, randomDelay);

        expect(mockPrismaService.client.$transaction).toHaveBeenCalled();
      });

      it('should handle Brazil timezone correctly for early morning', async () => {
        const firstMessageData = { ...baseMessageData, isFirstMessage: true };
        const earlyMorning = new Date('2025-08-01T10:00:00.000Z'); // 7:00 AM Brazil time
        jest.setSystemTime(earlyMorning);

        mockPrismaService.client.customerPhone.findFirst.mockResolvedValue(mockCustomerPhone);

        mockPrismaService.client.$transaction.mockImplementation(async callback => {
          const mockTx = {
            $queryRaw: jest
              .fn()
              .mockResolvedValueOnce([{ maxTimeToGo: earlyMorning }])
              .mockResolvedValueOnce([{ count: 0 }])
              .mockResolvedValueOnce([]),
            $executeRaw: jest.fn(),
            customerPhone: mockPrismaService.client.customerPhone,
          };
          return callback(mockTx);
        });

        await adapter.insertOutgoingMessage(firstMessageData, randomDelay);

        expect(mockPrismaService.client.$transaction).toHaveBeenCalled();
        jest.setSystemTime(FIXED_DATE);
      });
    });

    describe('Error handling and edge cases', () => {
      it('should throw error when customer phone not found', async () => {
        const firstMessageData = { ...baseMessageData, isFirstMessage: true };
        mockPrismaService.client.customerPhone.findFirst.mockResolvedValue(null);

        await expect(adapter.insertOutgoingMessage(firstMessageData, randomDelay)).rejects.toThrow(
          `Customer phone not found for number: ${baseMessageData.from} and channel: ${baseMessageData.channel}`,
        );
      });

      it('should handle database transaction errors', async () => {
        mockPrismaService.client.customerPhone.findFirst.mockResolvedValue(mockCustomerPhone);
        mockPrismaService.client.$transaction.mockRejectedValue(new Error('Database error'));

        await expect(adapter.insertOutgoingMessage(baseMessageData, randomDelay)).rejects.toThrow(
          'Error inserting outgoing message: Error: Database error',
        );
      });

      it('should handle zero random delay', async () => {
        mockPrismaService.client.customerPhone.findFirst.mockResolvedValue(mockCustomerPhone);

        mockPrismaService.client.$transaction.mockImplementation(async callback => {
          const mockTx = {
            $queryRaw: jest.fn().mockResolvedValue([{ maxTimeToGo: new Date(FIXED_DATE) }]),
            $executeRaw: jest.fn(),
            customerPhone: mockPrismaService.client.customerPhone,
          };
          return callback(mockTx);
        });

        await adapter.insertOutgoingMessage(baseMessageData, 0);

        expect(mockPrismaService.client.$transaction).toHaveBeenCalled();
      });
    });
  });
});
