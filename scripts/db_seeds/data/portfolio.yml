portfolios:
  - id: 30b0e1fb-64c7-4519-93aa-4eb5dd33087e
    name: TW Capital clients
    customerId: '4cd6d515-2604-4c2c-adad-435acbef1f5c'
    workflowId: '43e3ceed-58da-4ff2-bddf-67cb79d4433f'
    originalFileName: 'tw-capital-clients.csv'
    fileUrl: 'http://collectcash.s3.localhost.localstack.cloud:4566/4cd6d515-2604-4c2c-adad-435acbef1f5c/30b0e1fb-64c7-4519-93aa-4eb5dd33087e.csv'
    workExpression: '* * * * * *'
    followUpExpression: '* * * * * *'
    followUpAfter: 10
    followUpWorkflowId: c1314615-94bf-49d1-9b55-046a391ae0b4
    maxFollowUps: 1
    totalQuantity: 100
    executeImmediately: true
    processingRateLimit: 100
    executionStatus: 'QUEUED'
    communicationChannel: 'WHATSAPPSELFHOSTED'
    idleAfter: 7
    isDefault: false

  # - id: 123e4567-e89b-12d3-a456-426614174000
  #   name: Fishing NET portfolio
  #   customerId: '4cd6d515-2604-4c2c-adad-435acbef1f5c'
  #   workflowId: 'f3e3ceed-58da-4ff2-58da-67cb79d4433a'
  #   originalFileName: 'default_portfolio_file_name.csv'
  #   fileUrl: 'default_portfolio_file_url'
  #   workExpression: '* * * * * *'
  #   totalQuantity: 0
  #   executeImmediately: false
  #   importStatus: 'SUCCESS'
  #   importFinishedAt: '2025-03-12 13:51:44.111Z'
  #   processingRateLimit: 100
  #   executionStatus: 'INBOUND_EXECUTING'
  #   communicationChannel: 'WHATSAPPSELFHOSTED'
  #   idleAfter: 7
  #   isDefault: true
